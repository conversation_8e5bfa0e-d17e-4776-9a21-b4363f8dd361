import cv2
import numpy as np
import os
from license_plate_recognition import LicensePlateRecognition

def main():
    # Initialize the license plate recognition system
    lpr = LicensePlateRecognition()
    
    # Create test images with the specific license plates
    # These are synthetic images with the license plates you mentioned
    
    # Create a blank image (white background)
    img_height, img_width = 300, 600
    white_img = np.ones((img_height, img_width, 3), dtype=np.uint8) * 255
    
    # Draw the license plates on the image
    plates = [
        "MH01BT1699",  # White Maruti Suzuki Alto
        "AP09AF153",   # White Maruti Suzuki Omni van
        "RJ14CV0002"   # Standard Indian plate
    ]
    
    # Create a black rectangle for the license plate
    plate_width, plate_height = 200, 50
    x = (img_width - plate_width) // 2
    y = (img_height - plate_height) // 2
    
    for i, plate_text in enumerate(plates):
        # Create a copy of the white image
        img = white_img.copy()
        
        # Draw a black rectangle for the license plate
        cv2.rectangle(img, (x, y), (x + plate_width, y + plate_height), (0, 0, 0), 2)
        
        # Draw the license plate text
        cv2.putText(img, plate_text, (x + 10, y + 35), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Save the test image
        test_path = f"test_images/synthetic_{plate_text}.jpg"
        cv2.imwrite(test_path, img)
        print(f"Created test image: {test_path}")
        
        # Process the image
        result_img, results = lpr.process_image(img)
        
        # Display results
        print(f"\nResults for {plate_text}:")
        if results:
            for result in results:
                detected_plate = result['plate_number']
                confidence = result['confidence']
                print(f"Detected plate: {detected_plate}, Confidence: {confidence:.2f}%")
                if detected_plate == plate_text:
                    print("✓ MATCH!")
                else:
                    print("✗ NO MATCH")
        else:
            print("No license plates detected")
        
        # Save the result image
        result_path = f"results_synthetic_{plate_text}.jpg"
        cv2.imwrite(result_path, result_img)
        print(f"Result image saved to {result_path}")

if __name__ == "__main__":
    main()
