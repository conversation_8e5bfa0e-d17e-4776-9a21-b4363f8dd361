import cv2
from matplotlib import pyplot as plt
import numpy as np
import easyocr 
import imutils
import argparse
import re
import os
from skimage.filters import threshold_local

def load_image(image_path):
    """
    Load an image from the given path, URL, or base64 string with support for multiple formats.

    Args:
        image_path: Path to the image file, URL, or base64 string

    Returns:
        numpy.ndarray: Loaded image or None if loading failed
    """
    try:
        # Check if it's a URL
        if image_path.startswith(('http://', 'https://')):
            return load_image_from_url(image_path)

        # Check if it's a base64 string
        if image_path.startswith(('data:image/', 'base64:')):
            return load_image_from_base64(image_path)

        # Check if file exists
        if not os.path.exists(image_path):
            print(f"Error: File '{image_path}' does not exist.")
            return None

        # Get file extension
        _, file_extension = os.path.splitext(image_path.lower())

        # Try to load with OpenCV first (supports jpg, png, bmp, etc.)
        img = cv2.imread(image_path)

        # If OpenCV fails, try other methods based on extension
        if img is None:
            try:
                # For JPEG/JPG files that OpenCV couldn't read
                if file_extension in ['.jpg', '.jpeg', '.jpe', '.jfif', '.jepg']:
                    # Try using PIL/Pillow as a fallback
                    from PIL import Image
                    pil_img = Image.open(image_path)
                    img = np.array(pil_img)
                    # Convert RGB to BGR (OpenCV format)
                    if len(img.shape) == 3 and img.shape[2] == 3:
                        img = img[:, :, ::-1]

                # For TIFF files
                elif file_extension in ['.tif', '.tiff']:
                    from PIL import Image
                    pil_img = Image.open(image_path)
                    img = np.array(pil_img)
                    if len(img.shape) == 3 and img.shape[2] == 3:
                        img = img[:, :, ::-1]

                # For WEBP files
                elif file_extension == '.webp':
                    from PIL import Image
                    pil_img = Image.open(image_path)
                    img = np.array(pil_img)
                    if len(img.shape) == 3 and img.shape[2] == 3:
                        img = img[:, :, ::-1]

                # For other formats, try PIL as a general fallback
                else:
                    try:
                        from PIL import Image
                        pil_img = Image.open(image_path)
                        img = np.array(pil_img)
                        if len(img.shape) == 3 and img.shape[2] == 3:
                            img = img[:, :, ::-1]
                    except Exception as e:
                        print(f"Error: Unsupported image format '{file_extension}'. {str(e)}")
                        return None

            except ImportError:
                print("Warning: PIL/Pillow is not installed. Limited image format support.")
                print("Install with: pip install pillow")
                return None
            except Exception as e:
                print(f"Error loading image: {str(e)}")
                return None

        return img
    except Exception as e:
        print(f"Unexpected error loading image: {str(e)}")
        return None

def load_image_from_base64(base64_string):
    """
    Load an image from a base64 string.

    Args:
        base64_string: Base64-encoded image string

    Returns:
        numpy.ndarray: Loaded image or None if loading failed
    """
    try:
        # Try to import required libraries
        try:
            import base64
            from PIL import Image
            import io
        except ImportError:
            print("Warning: Required libraries not installed.")
            print("Install with: pip install pillow")
            return None

        # Handle data URI scheme
        if base64_string.startswith('data:image/'):
            # Extract the base64 part
            base64_string = base64_string.split(',')[1]
        elif base64_string.startswith('base64:'):
            base64_string = base64_string[7:]

        # Decode base64 string
        image_data = base64.b64decode(base64_string)

        # Create a BytesIO object
        image_buffer = io.BytesIO(image_data)

        # Open the image with PIL
        pil_img = Image.open(image_buffer)
        img = np.array(pil_img)

        # Convert RGB to BGR (OpenCV format) if needed
        if len(img.shape) == 3 and img.shape[2] == 3:
            img = img[:, :, ::-1]

        return img
    except Exception as e:
        print(f"Error loading image from base64: {str(e)}")
        return None

def load_image_from_url(url):
    """
    Load an image from a URL.

    Args:
        url: URL of the image

    Returns:
        numpy.ndarray: Loaded image or None if loading failed
    """
    try:
        # Try to import required libraries
        try:
            import urllib.request
            from PIL import Image
        except ImportError:
            print("Warning: Required libraries not installed.")
            print("Install with: pip install pillow")
            return None

        # Create a temporary file to store the image
        temp_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_image")

        # Download the image
        urllib.request.urlretrieve(url, temp_file)

        # Open the image with PIL
        pil_img = Image.open(temp_file)
        img = np.array(pil_img)

        # Convert RGB to BGR (OpenCV format) if needed
        if len(img.shape) == 3 and img.shape[2] == 3:
            img = img[:, :, ::-1]

        # Remove the temporary file
        try:
            os.remove(temp_file)
        except:
            pass

        return img
    except Exception as e:
        print(f"Error loading image from URL: {str(e)}")
        return None

def preprocess_image(img):
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply CLAHE for better contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    gray = clahe.apply(gray)

    # Apply bilateral filter to preserve edges while reducing noise
    bfilter = cv2.bilateralFilter(gray, 11, 17, 17)

    return gray, bfilter

def detect_license_plate(img, bfilter):
    # Try multiple detection methods and combine results
    plates = []

    # Method 1: Contour-based detection with adaptive thresholds
    plates_contour = detect_plates_contour(bfilter)
    if plates_contour:
        plates.extend(plates_contour)

    # Method 2: Rectangle detection with Hough transform
    plates_hough = detect_plates_hough(bfilter)
    if plates_hough:
        plates.extend(plates_hough)

    # Method 3: Text-based detection (look for text-rich areas)
    plates_text = detect_plates_text(img, bfilter)
    if plates_text:
        plates.extend(plates_text)

    # Remove duplicates and sort by confidence
    plates = sorted(plates, key=lambda x: x[1], reverse=True)

    if plates:
        return plates[0][0]  # Return the highest confidence plate location
    return None

def detect_plates_contour(bfilter):
    plates = []

    # Compute adaptive Canny thresholds based on image median
    median = np.median(bfilter)
    lower = int(max(0, 0.66 * median))
    upper = int(min(255, 1.33 * median))

    # Apply Canny edge detection with adaptive thresholds
    edged = cv2.Canny(bfilter, lower, upper)

    # Apply morphological operations to connect nearby edges
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    edged = cv2.morphologyEx(edged, cv2.MORPH_CLOSE, kernel)

    # Find contours
    keypoints = cv2.findContours(edged.copy(), cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    contours = imutils.grab_contours(keypoints)
    contours = sorted(contours, key=cv2.contourArea, reverse=True)[:15]  # Check more contours

    for contour in contours:
        # Try different epsilon values for polygon approximation
        for epsilon_factor in [0.02, 0.03, 0.04]:
            peri = cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon_factor * peri, True)

            # Check if it's a quadrilateral (4 points)
            if len(approx) == 4:
                # Get bounding rectangle dimensions (x and y position not used)
                _, _, w, h = cv2.boundingRect(approx)
                aspect_ratio = w / float(h)

                # More flexible aspect ratio check for different plate types
                if 1.5 < aspect_ratio < 7:
                    # Calculate confidence based on shape regularity and size
                    confidence = calculate_plate_confidence(approx, w, h)
                    plates.append((approx, confidence))

    return plates

def detect_plates_hough(bfilter):
    plates = []

    # Apply threshold to get binary image
    _, thresh = cv2.threshold(bfilter, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Detect lines using Hough transform
    lines = cv2.HoughLinesP(thresh, 1, np.pi/180, threshold=100, minLineLength=100, maxLineGap=10)

    if lines is not None:
        # Group lines into potential rectangles
        horizontal_lines = []
        vertical_lines = []

        for line in lines:
            x1, y1, x2, y2 = line[0]
            if abs(x2 - x1) > abs(y2 - y1):  # Horizontal line
                horizontal_lines.append(line[0])
            else:  # Vertical line
                vertical_lines.append(line[0])

        # Find intersections of horizontal and vertical lines
        # This is simplified - a real implementation would need more complex logic
        if len(horizontal_lines) >= 2 and len(vertical_lines) >= 2:
            # Take the outermost lines to form a rectangle
            h_sorted = sorted(horizontal_lines, key=lambda x: x[1])
            v_sorted = sorted(vertical_lines, key=lambda x: x[0])

            top, bottom = h_sorted[0], h_sorted[-1]
            left, right = v_sorted[0], v_sorted[-1]

            # Create a quadrilateral from the lines
            points = np.array([
                [[left[0], top[1]]],
                [[right[0], top[1]]],
                [[right[0], bottom[1]]],
                [[left[0], bottom[1]]]
            ])

            # Calculate width and height
            w = right[0] - left[0]
            h = bottom[1] - top[1]

            aspect_ratio = w / float(h)
            if 1.5 < aspect_ratio < 7:
                confidence = 0.7  # Default confidence for Hough method
                plates.append((points, confidence))

    return plates

def detect_plates_text(img, bfilter):
    plates = []
    height, width = img.shape[:2]

    # Create a grid of potential plate regions
    grid_size = 3
    cell_height = height // grid_size
    cell_width = width // grid_size

    for i in range(grid_size):
        for j in range(grid_size):
            # Define region
            y1 = i * cell_height
            y2 = (i + 1) * cell_height
            x1 = j * cell_width
            x2 = (j + 1) * cell_width

            # Extract region
            region = bfilter[y1:y2, x1:x2]

            if region.size == 0:
                continue

            # Calculate features that might indicate text
            # 1. Edge density
            edges = cv2.Canny(region, 50, 150)
            edge_density = np.sum(edges) / (region.shape[0] * region.shape[1])

            # 2. Horizontal gradient (text has strong horizontal gradients)
            sobelx = cv2.Sobel(region, cv2.CV_64F, 1, 0, ksize=3)
            abs_sobelx = np.absolute(sobelx)
            if np.max(abs_sobelx) > 0:
                scaled_sobel = np.uint8(255 * abs_sobelx / np.max(abs_sobelx))
                horizontal_gradient = np.mean(scaled_sobel)
            else:
                horizontal_gradient = 0

            # 3. Variance in intensity (text regions have high variance)
            intensity_variance = np.var(region)

            # Combined score
            score = edge_density * 0.4 + horizontal_gradient * 0.4 + intensity_variance * 0.2

            if score > 30:  # Threshold for text-like regions
                # Create a rectangle for this region
                points = np.array([
                    [[x1, y1]],
                    [[x2, y1]],
                    [[x2, y2]],
                    [[x1, y2]]
                ])

                confidence = score / 100  # Normalize score to 0-1 range
                plates.append((points, confidence))

    return plates

def calculate_plate_confidence(approx, width, height):
    # Calculate confidence based on shape regularity and size
    # 1. Check if it's a proper rectangle (angles close to 90 degrees)
    angles = []
    for i in range(4):
        p1 = approx[i][0]
        p2 = approx[(i+1)%4][0]
        p3 = approx[(i+2)%4][0]

        # Calculate vectors
        v1 = p1 - p2
        v2 = p3 - p2

        # Calculate angle
        dot = np.dot(v1, v2)
        norm = np.linalg.norm(v1) * np.linalg.norm(v2)
        if norm == 0:
            angle = 0
        else:
            angle = np.arccos(dot / norm) * 180 / np.pi
        angles.append(angle)

    # Ideal rectangle has 90 degree angles
    angle_score = 1 - min(sum([abs(angle - 90) for angle in angles]) / 360, 1)

    # 2. Size score - prefer larger plates but not too large
    size_score = min((width * height) / 10000, 1)

    # 3. Aspect ratio score - closer to typical license plate ratio (around 4:1)
    aspect_ratio = width / float(height)
    aspect_score = 1 - min(abs(aspect_ratio - 4) / 3, 1)

    # Combined confidence
    confidence = 0.4 * angle_score + 0.3 * size_score + 0.3 * aspect_score

    return confidence

def enhance_plate_image(plate_img):
    # Apply multiple enhancement techniques
    enhanced_images = []

    # 1. Original grayscale
    enhanced_images.append(("original", plate_img))

    # 2. CLAHE enhancement
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(plate_img)
    enhanced_images.append(("clahe", enhanced))

    # 3. Adaptive thresholding - Gaussian
    thresh_gauss = cv2.adaptiveThreshold(plate_img, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 11, 2)
    enhanced_images.append(("thresh_gauss", thresh_gauss))

    # 4. Adaptive thresholding - Mean
    thresh_mean = cv2.adaptiveThreshold(plate_img, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                        cv2.THRESH_BINARY, 11, 2)
    enhanced_images.append(("thresh_mean", thresh_mean))

    # 5. Local thresholding
    thresh_local = threshold_local(plate_img, 35, offset=10, method="gaussian")
    binary_local = (plate_img > thresh_local).astype("uint8") * 255
    enhanced_images.append(("thresh_local", binary_local))

    # 6. Otsu's thresholding
    _, thresh_otsu = cv2.threshold(plate_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    enhanced_images.append(("thresh_otsu", thresh_otsu))

    # 7. Inverted versions for dark text on light background
    for name, img in enhanced_images.copy():
        enhanced_images.append((f"{name}_inv", cv2.bitwise_not(img)))

    return enhanced_images

def segment_characters(img, visualize=False):
    """
    Segment characters in a license plate image.

    Args:
        img: Grayscale image of a license plate
        visualize: Whether to return visualization images

    Returns:
        list: List of character images and their positions
        dict: Visualization images (if visualize=True)
    """
    # Make a copy of the image
    img_copy = img.copy()

    # Apply threshold to get binary image
    _, binary = cv2.threshold(img_copy, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # Apply morphological operations to clean up the image
    kernel = np.ones((3, 3), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

    # Find contours
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter contours by size and shape
    char_contours = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / float(h)
        area = cv2.contourArea(contour)

        # Character aspect ratio is typically between 0.2 and 1.0
        # and height is typically at least 40% of the image height
        if 0.1 < aspect_ratio < 1.0 and h > img.shape[0] * 0.4 and area > 100:
            char_contours.append((x, y, w, h, contour))

    # Sort contours from left to right
    char_contours.sort(key=lambda x: x[0])

    # Extract character images
    char_images = []

    # Create visualization images if requested
    vis_images = {}
    if visualize:
        # Create a color version of the original image for visualization
        vis_original = cv2.cvtColor(img.copy(), cv2.COLOR_GRAY2BGR)
        vis_binary = cv2.cvtColor(binary.copy(), cv2.COLOR_GRAY2BGR)

        # Draw all contours on binary image
        cv2.drawContours(vis_binary, contours, -1, (0, 255, 0), 1)
        vis_images['all_contours'] = vis_binary

        # Draw filtered contours on original image
        vis_filtered = vis_original.copy()
        for x, y, w, h, contour in char_contours:
            cv2.rectangle(vis_filtered, (x, y), (x+w, y+h), (0, 255, 0), 2)
        vis_images['filtered_contours'] = vis_filtered

    for x, y, w, h, contour in char_contours:
        # Add padding around the character
        padding = 2
        x_start = max(0, x - padding)
        y_start = max(0, y - padding)
        x_end = min(img.shape[1], x + w + padding)
        y_end = min(img.shape[0], y + h + padding)

        # Extract the character
        char_img = img[y_start:y_end, x_start:x_end]

        # Skip very small images
        if char_img.size == 0 or char_img.shape[0] < 5 or char_img.shape[1] < 5:
            continue

        char_images.append((char_img, (x, y, w, h)))

    if visualize:
        # Create a visualization of the segmented characters
        vis_segmented = vis_original.copy()
        for i, (_, (x, y, w, h)) in enumerate(char_images):
            # Draw rectangle around character
            cv2.rectangle(vis_segmented, (x, y), (x+w, y+h), (0, 255, 0), 2)
            # Add character index
            cv2.putText(vis_segmented, str(i+1), (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        vis_images['segmented_chars'] = vis_segmented

        # Create a grid of the extracted characters
        if char_images:
            # Determine grid size
            grid_cols = min(len(char_images), 10)
            grid_rows = (len(char_images) + grid_cols - 1) // grid_cols

            # Find maximum character dimensions
            max_h = max([img.shape[0] for img, _ in char_images])
            max_w = max([img.shape[1] for img, _ in char_images])

            # Create grid image with padding
            padding = 10
            grid_h = grid_rows * (max_h + padding) + padding
            grid_w = grid_cols * (max_w + padding) + padding
            grid_img = np.ones((grid_h, grid_w), dtype=np.uint8) * 255

            # Place characters in grid
            for i, (char_img, _) in enumerate(char_images):
                row = i // grid_cols
                col = i % grid_cols
                y = row * (max_h + padding) + padding
                x = col * (max_w + padding) + padding

                # Center character in its cell
                h, w = char_img.shape
                y_offset = (max_h - h) // 2
                x_offset = (max_w - w) // 2

                grid_img[y+y_offset:y+y_offset+h, x+x_offset:x+x_offset+w] = char_img

                # Add character index
                cv2.putText(grid_img, str(i+1), (x, y-2), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 0, 1)

            vis_images['char_grid'] = grid_img

        return char_images, vis_images

    return char_images

def read_text_with_multiple_methods(plate_images, reader, show_steps=False):
    all_results = []
    segmentation_results = []
    segmentation_visualizations = {}

    # Try OCR on each enhanced image
    for name, img in plate_images:
        try:
            # 1. Standard EasyOCR on the whole image
            results = reader.readtext(img)
            for result in results:
                confidence = result[2]
                text = result[1]
                all_results.append((text, confidence, "easyocr", name))

            # 2. Try character segmentation approach
            if show_steps:
                char_images, vis_images = segment_characters(img, visualize=True)
                # Store visualizations for this image
                segmentation_visualizations[name] = vis_images
            else:
                char_images = segment_characters(img)

            if char_images and len(char_images) >= 4:  # At least 4 characters for a license plate
                # Recognize each character
                chars = []
                for i, (char_img, _) in enumerate(char_images):
                    # Resize for better OCR
                    char_img = cv2.resize(char_img, (28, 28), interpolation=cv2.INTER_CUBIC)

                    # Apply threshold
                    _, char_binary = cv2.threshold(char_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

                    # Recognize the character
                    char_results = reader.readtext(char_binary)
                    if char_results:
                        char_text = char_results[0][1]
                        char_conf = char_results[0][2]
                        # Keep only alphanumeric
                        char_text = ''.join(c for c in char_text if c.isalnum())
                        if char_text:
                            chars.append((char_text[0], char_conf))
                            if show_steps and 'char_recognition' not in segmentation_visualizations[name]:
                                segmentation_visualizations[name]['char_recognition'] = {}
                            if show_steps:
                                segmentation_visualizations[name]['char_recognition'][i] = (char_text[0], char_conf)

                if chars:
                    # Combine characters into a single text
                    segmented_text = ''.join([c[0] for c in chars])
                    avg_confidence = sum([c[1] for c in chars]) / len(chars)
                    segmentation_results.append((segmented_text, avg_confidence, "segmentation", name))

                    if show_steps:
                        segmentation_visualizations[name]['segmented_text'] = segmented_text
                        segmentation_visualizations[name]['segmented_confidence'] = avg_confidence
        except Exception as e:
            print(f"Error with OCR on {name}: {str(e)}")

    # Combine results
    all_results.extend(segmentation_results)

    # Sort by confidence
    all_results.sort(key=lambda x: x[1], reverse=True)

    if show_steps:
        # Process the results to get the final text and confidence
        final_text, final_confidence = process_ocr_results(all_results)
        return final_text, final_confidence, all_results, segmentation_visualizations
    else:
        # Process the results to get the final text and confidence
        final_text, final_confidence = process_ocr_results(all_results)
        return final_text, final_confidence

def process_ocr_results(all_results):
    """
    Process OCR results to extract the most likely license plate text.

    Args:
        all_results: List of OCR results with (text, confidence, method, img_type)

    Returns:
        tuple: (text, confidence)
    """
    if not all_results:
        return "No text detected", 0

    # Post-process results
    for text, confidence, _, _ in all_results:  # Ignore method and img_type
        # Clean text - keep only alphanumeric
        clean_text = ''.join(c for c in text if c.isalnum())

        if clean_text and len(clean_text) >= 4:
            # Try to match Indian license plate patterns

            # 1. Modern Indian format: AA00AA0000
            # State code (2 letters) + District code (2 digits) + Series (2 letters) + Number (4 digits)
            indian_pattern = re.compile(r'([A-Z]{2}\s*\d{1,2}\s*[A-Z]{1,2}\s*\d{1,4})')
            indian_match = indian_pattern.search(clean_text)
            if indian_match:
                return indian_match.group(1), confidence * 100

            # 2. Look for state code followed by numbers (e.g., KA1AA2345)
            state_codes = ['AP', 'AR', 'AS', 'BR', 'CG', 'CH', 'DD', 'DL', 'DN', 'GA', 'GJ',
                          'HP', 'HR', 'JH', 'JK', 'KA', 'KL', 'LA', 'LD', 'MH', 'ML', 'MN',
                          'MP', 'MZ', 'NL', 'OD', 'PB', 'PY', 'RJ', 'SK', 'TN', 'TR', 'TS',
                          'UK', 'UP', 'WB']

            for code in state_codes:
                if clean_text.startswith(code) and len(clean_text) >= 6:
                    # Found a state code at the beginning
                    return clean_text, confidence * 100

            # 3. Standard pattern (e.g., ABC1234) as fallback
            pattern = re.compile(r'([A-Z0-9]{4,12})')
            match = pattern.search(clean_text)

            if match:
                return match.group(1), confidence * 100

    # If no pattern matches, return the highest confidence text
    best_text = ''.join(c for c in all_results[0][0] if c.isalnum())
    return best_text, all_results[0][1] * 100

def is_closeup_plate(img):
    """
    Determine if an image is likely a close-up of a license plate.

    Args:
        img: Input image (grayscale or color)

    Returns:
        bool: True if the image is likely a close-up of a license plate
    """
    # Convert to grayscale if needed
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if len(img.shape) > 2 else img
    height, width = gray.shape[:2]

    # Calculate edge density
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.sum(edges) / (height * width)

    # Calculate horizontal gradient (text has strong horizontal gradients)
    sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    abs_sobelx = np.absolute(sobelx)
    if np.max(abs_sobelx) > 0:
        scaled_sobel = np.uint8(255 * abs_sobelx / np.max(abs_sobelx))
        horizontal_gradient = np.mean(scaled_sobel)
    else:
        horizontal_gradient = 0

    # Calculate text-like features
    # 1. Variance in intensity (text regions have high variance)
    intensity_variance = np.var(gray)

    # 2. Check for horizontal lines (license plates have horizontal borders)
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=width//3, maxLineGap=20)
    horizontal_lines_count = 0
    if lines is not None:
        for line in lines:
            _, y1, _, y2 = line[0]  # We only care about y-coordinates for horizontal line detection
            if abs(y2 - y1) < 10:  # Nearly horizontal line
                horizontal_lines_count += 1

    # Combined score
    score = (edge_density * 0.3 +
             horizontal_gradient * 0.3 +
             (intensity_variance / 1000) * 0.2 +
             min(horizontal_lines_count / 5, 1.0) * 0.2)

    # Debug information
    print(f"Close-up detection scores: Edge density: {edge_density:.2f}, "
          f"Horizontal gradient: {horizontal_gradient:.2f}, "
          f"Intensity variance: {intensity_variance:.2f}, "
          f"Horizontal lines: {horizontal_lines_count}, "
          f"Combined score: {score:.2f}")

    # If score is high across the whole image, it's likely a close-up
    return score > 0.4

def correct_perspective(img):
    """
    Attempt to correct perspective distortion in license plate images.

    Args:
        img: Grayscale image of a license plate

    Returns:
        list: List of corrected images with different parameters
    """
    corrected_images = []

    # Add the original image
    corrected_images.append(("original", img))

    # Apply edge detection
    edges = cv2.Canny(img, 50, 150)

    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if contours:
        # Find the largest contour
        largest_contour = max(contours, key=cv2.contourArea)

        # Approximate the contour to get a polygon
        epsilon = 0.02 * cv2.arcLength(largest_contour, True)
        approx = cv2.approxPolyDP(largest_contour, epsilon, True)

        # If we have a quadrilateral, correct perspective
        if len(approx) == 4:
            # Order points in top-left, top-right, bottom-right, bottom-left order
            pts = np.array([pt[0] for pt in approx])
            rect = order_points(pts)

            # Get width and height of the destination image
            width = max(
                int(np.sqrt(((rect[0][0] - rect[1][0]) ** 2) + ((rect[0][1] - rect[1][1]) ** 2))),
                int(np.sqrt(((rect[2][0] - rect[3][0]) ** 2) + ((rect[2][1] - rect[3][1]) ** 2)))
            )
            height = max(
                int(np.sqrt(((rect[0][0] - rect[3][0]) ** 2) + ((rect[0][1] - rect[3][1]) ** 2))),
                int(np.sqrt(((rect[1][0] - rect[2][0]) ** 2) + ((rect[1][1] - rect[2][1]) ** 2)))
            )

            # Define destination points
            dst = np.array([
                [0, 0],
                [width - 1, 0],
                [width - 1, height - 1],
                [0, height - 1]
            ], dtype="float32")

            # Calculate perspective transform matrix
            M = cv2.getPerspectiveTransform(rect, dst)

            # Apply perspective transformation
            warped = cv2.warpPerspective(img, M, (width, height))
            corrected_images.append(("perspective_corrected", warped))

    # Try rotation correction based on horizontal lines
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=30, maxLineGap=10)
    if lines is not None:
        angles = []
        for line in lines:
            x1, y1, x2, y2 = line[0]
            # Only consider nearly horizontal lines
            if abs(y2 - y1) < 10 and abs(x2 - x1) > 20:
                angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi
                angles.append(angle)

        if angles:
            # Use median angle to avoid outliers
            median_angle = np.median(angles)

            # Rotate image to correct skew
            h, w = img.shape[:2]
            center = (w // 2, h // 2)
            M = cv2.getRotationMatrix2D(center, median_angle, 1.0)
            rotated = cv2.warpAffine(img, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
            corrected_images.append(("rotation_corrected", rotated))

    return corrected_images

def order_points(pts):
    """
    Order points in top-left, top-right, bottom-right, bottom-left order.

    Args:
        pts: Array of 4 points

    Returns:
        Ordered points as float32 array
    """
    # Initialize ordered points array
    rect = np.zeros((4, 2), dtype="float32")

    # Top-left point has the smallest sum
    # Bottom-right point has the largest sum
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]
    rect[2] = pts[np.argmax(s)]

    # Top-right point has the smallest difference
    # Bottom-left point has the largest difference
    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmin(diff)]
    rect[3] = pts[np.argmax(diff)]

    return rect

def enhance_closeup_plate(img):
    """
    Apply specialized enhancement techniques for close-up license plate images.

    Args:
        img: Grayscale image of a license plate

    Returns:
        list: List of tuples (name, enhanced_image)
    """
    enhanced_images = []

    # First try to correct perspective/rotation
    corrected_images = correct_perspective(img)

    # Process each corrected image
    for correction_name, corrected_img in corrected_images:
        # 1. Original grayscale
        enhanced_images.append((f"{correction_name}_original", corrected_img))

        # 2. Denoising
        denoised = cv2.fastNlMeansDenoising(corrected_img, None, 10, 7, 21)
        enhanced_images.append((f"{correction_name}_denoised", denoised))

        # 3. CLAHE enhancement with different parameters
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(corrected_img)
        enhanced_images.append((f"{correction_name}_clahe", enhanced))

        # 4. Stronger CLAHE
        clahe_strong = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(4, 4))
        enhanced_strong = clahe_strong.apply(corrected_img)
        enhanced_images.append((f"{correction_name}_clahe_strong", enhanced_strong))

        # 5. Sharpen the image
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(corrected_img, -1, kernel)
        enhanced_images.append((f"{correction_name}_sharpened", sharpened))

        # 6. Adaptive thresholding - Gaussian with smaller block size
        thresh_gauss = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 9, 2)
        enhanced_images.append((f"{correction_name}_thresh_gauss", thresh_gauss))

        # 7. Adaptive thresholding - Mean with smaller block size
        thresh_mean = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                        cv2.THRESH_BINARY, 9, 2)
        enhanced_images.append((f"{correction_name}_thresh_mean", thresh_mean))

        # 8. Local thresholding with smaller window
        thresh_local = threshold_local(corrected_img, 25, offset=10, method="gaussian")
        binary_local = (corrected_img > thresh_local).astype("uint8") * 255
        enhanced_images.append((f"{correction_name}_thresh_local", binary_local))

        # 9. Otsu's thresholding
        _, thresh_otsu = cv2.threshold(corrected_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        enhanced_images.append((f"{correction_name}_thresh_otsu", thresh_otsu))

    # 10. Inverted versions for dark text on light background
    for name, img in enhanced_images.copy():
        enhanced_images.append((f"{name}_inv", cv2.bitwise_not(img)))

    return enhanced_images

def process_image_data(img_data, show_steps=True):
    """
    Process image data directly (numpy array).

    Args:
        img_data: Image data as numpy array
        show_steps: Whether to show intermediate steps

    Returns:
        tuple: (result_image, text, confidence)
    """
    if img_data is None:
        print("Error: Invalid image data.")
        return None, "No image data", 0

    # Make a copy for results
    result_img = img_data.copy()

    # Preprocess image
    gray, bfilter = preprocess_image(img_data)

    # Continue with the rest of the processing...
    if show_steps:
        plt.figure(figsize=(10, 5))
        plt.subplot(121)
        plt.imshow(cv2.cvtColor(img_data, cv2.COLOR_BGR2RGB))
        plt.title('Original Image')
        plt.subplot(122)
        plt.imshow(gray, cmap='gray')
        plt.title('Preprocessed Image')
        plt.show()

    # Check if the image is a close-up of a license plate
    closeup = is_closeup_plate(img_data)
    print(f"Is close-up license plate: {closeup}")

    # Continue with the same processing logic as in process_image
    # ... (rest of the function is identical to process_image)

    if closeup:
        print("Processing as close-up license plate image...")
        # Apply specialized enhancement for close-up plates
        enhanced_plates = enhance_closeup_plate(gray)

        if show_steps:
            # Show a sample of enhanced images
            plt.figure(figsize=(15, 10))
            for i, (name, img) in enumerate(enhanced_plates[:6]):
                plt.subplot(2, 3, i+1)
                plt.imshow(img, cmap='gray')
                plt.title(f'Enhancement: {name}')
            plt.tight_layout()
            plt.show()

        # Initialize OCR reader
        reader = easyocr.Reader(['en'])

        # Read text with multiple methods and post-processing
        if show_steps:
            text, confidence, all_results, segmentation_vis = read_text_with_multiple_methods(enhanced_plates, reader, show_steps=True)
            print(f"Detected Text (Close-up): {text} (Confidence: {confidence:.2f}%)")

            # Show character segmentation visualizations for the best result
            best_img_name = None
            for _, _, method, img_name in all_results:  # Ignore text and confidence
                if method == "segmentation":
                    best_img_name = img_name
                    break

            if best_img_name and best_img_name in segmentation_vis:
                # Show character segmentation
                plt.figure(figsize=(15, 10))
                vis_count = 0

                if 'segmented_chars' in segmentation_vis[best_img_name]:
                    vis_count += 1
                    plt.subplot(2, 2, vis_count)
                    plt.imshow(cv2.cvtColor(segmentation_vis[best_img_name]['segmented_chars'], cv2.COLOR_BGR2RGB))
                    plt.title('Character Segmentation')

                if 'char_grid' in segmentation_vis[best_img_name]:
                    vis_count += 1
                    plt.subplot(2, 2, vis_count)
                    plt.imshow(segmentation_vis[best_img_name]['char_grid'], cmap='gray')
                    plt.title('Individual Characters')

                if 'all_contours' in segmentation_vis[best_img_name]:
                    vis_count += 1
                    plt.subplot(2, 2, vis_count)
                    plt.imshow(cv2.cvtColor(segmentation_vis[best_img_name]['all_contours'], cv2.COLOR_BGR2RGB))
                    plt.title('All Contours')

                if 'filtered_contours' in segmentation_vis[best_img_name]:
                    vis_count += 1
                    plt.subplot(2, 2, vis_count)
                    plt.imshow(cv2.cvtColor(segmentation_vis[best_img_name]['filtered_contours'], cv2.COLOR_BGR2RGB))
                    plt.title('Filtered Contours')

                plt.tight_layout()
                plt.show()

                # Show segmentation result
                if 'segmented_text' in segmentation_vis[best_img_name]:
                    segmented_text = segmentation_vis[best_img_name]['segmented_text']
                    segmented_conf = segmentation_vis[best_img_name]['segmented_confidence']
                    print(f"Character-by-character segmentation result: {segmented_text} (Confidence: {segmented_conf:.2f})")
        else:
            text, confidence = read_text_with_multiple_methods(enhanced_plates, reader)
            print(f"Detected Text (Close-up): {text} (Confidence: {confidence:.2f}%)")

        # Draw results on image
        font = cv2.FONT_HERSHEY_SIMPLEX
        h, w = img_data.shape[:2]

        # Draw a rectangle around the entire image (since it's a close-up)
        cv2.rectangle(result_img, (10, 10), (w-10, h-10), (0, 255, 0), 3)

        # Position text at the top of the image
        cv2.putText(result_img, text, (w//10, 30),
                    font, 1, (0, 255, 0), 2, cv2.LINE_AA)
        cv2.putText(result_img, f"Conf: {confidence:.1f}%", (w//10, 70),
                    font, 0.8, (0, 255, 0), 2, cv2.LINE_AA)

        if show_steps:
            plt.figure(figsize=(10, 8))
            plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
            plt.title('Final Result (Close-up)')
            plt.show()

        return result_img, text, confidence

    # Standard processing for non-close-up images
    # Detect license plate
    location = detect_license_plate(img_data, bfilter)

    if location is None:
        print("No license plate detected. Trying direct OCR...")

        # Try direct OCR on the whole image as a fallback
        enhanced_images = enhance_plate_image(gray)

        # Initialize OCR reader
        reader = easyocr.Reader(['en'])

        # Read text with multiple methods
        if show_steps:
            text, confidence, all_results, segmentation_vis = read_text_with_multiple_methods(enhanced_images, reader, show_steps=True)

            if confidence > 0:
                print(f"Direct OCR: {text} (Confidence: {confidence:.2f}%)")

                # Show character segmentation visualizations if available
                best_img_name = None
                for _, _, method, img_name in all_results:
                    if method == "segmentation":
                        best_img_name = img_name
                        break

                if best_img_name and best_img_name in segmentation_vis:
                    # Show character segmentation visualizations
                    plt.figure(figsize=(15, 10))
                    vis_count = 0

                    if 'segmented_chars' in segmentation_vis[best_img_name]:
                        vis_count += 1
                        plt.subplot(2, 2, vis_count)
                        plt.imshow(cv2.cvtColor(segmentation_vis[best_img_name]['segmented_chars'], cv2.COLOR_BGR2RGB))
                        plt.title('Character Segmentation (Fallback)')

                    if 'char_grid' in segmentation_vis[best_img_name]:
                        vis_count += 1
                        plt.subplot(2, 2, vis_count)
                        plt.imshow(segmentation_vis[best_img_name]['char_grid'], cmap='gray')
                        plt.title('Individual Characters (Fallback)')

                    plt.tight_layout()
                    plt.show()
        else:
            text, confidence = read_text_with_multiple_methods(enhanced_images, reader)

            if confidence > 0:
                print(f"Direct OCR: {text} (Confidence: {confidence:.2f}%)")

        # Draw results on image
        font = cv2.FONT_HERSHEY_SIMPLEX
        h, w = img_data.shape[:2]
        cv2.putText(result_img, text, (w//10, h//2),
                    font, 1, (0, 255, 0), 2, cv2.LINE_AA)
        cv2.putText(result_img, f"Conf: {confidence:.1f}%", (w//10, h//2 + 30),
                    font, 0.8, (0, 255, 0), 2, cv2.LINE_AA)

        return result_img, text, confidence

    # Create mask and crop plate
    mask = np.zeros(gray.shape, np.uint8)
    cv2.drawContours(mask, [location], 0, 255, -1)
    new_image = cv2.bitwise_and(img_data, img_data, mask=mask)

    # Find coordinates of plate
    (x, y) = np.where(mask == 255)
    if len(x) == 0 or len(y) == 0:
        print("Error: Empty mask")
        return result_img, "Empty mask", 0

    (x1, y1) = (np.min(x), np.min(y))
    (x2, y2) = (np.max(x), np.max(y))
    plate_img = gray[x1:x2+1, y1:y2+1]

    if show_steps:
        plt.figure(figsize=(10, 5))
        plt.subplot(121)
        plt.imshow(cv2.cvtColor(new_image, cv2.COLOR_BGR2RGB))
        plt.title('Masked Image')
        plt.subplot(122)
        plt.imshow(plate_img, cmap='gray')
        plt.title('Cropped License Plate')
        plt.show()

    # Enhance plate image with multiple techniques
    enhanced_plates = enhance_plate_image(plate_img)

    if show_steps:
        # Show a sample of enhanced images
        plt.figure(figsize=(15, 10))
        for i, (name, img) in enumerate(enhanced_plates[:6]):
            plt.subplot(2, 3, i+1)
            plt.imshow(img, cmap='gray')
            plt.title(f'Enhancement: {name}')
        plt.tight_layout()
        plt.show()

    # Initialize OCR reader
    reader = easyocr.Reader(['en'])

    # Read text with multiple methods and post-processing
    if show_steps:
        text, confidence, all_results, segmentation_vis = read_text_with_multiple_methods(enhanced_plates, reader, show_steps=True)
        print(f"Detected Text: {text} (Confidence: {confidence:.2f}%)")

        # Show character segmentation visualizations for the best result
        best_img_name = None
        for _, _, method, img_name in all_results:  # Ignore text and confidence
            if method == "segmentation":
                best_img_name = img_name
                break

        if best_img_name and best_img_name in segmentation_vis:
            # Show character segmentation
            plt.figure(figsize=(15, 10))
            vis_count = 0

            if 'segmented_chars' in segmentation_vis[best_img_name]:
                vis_count += 1
                plt.subplot(2, 2, vis_count)
                plt.imshow(cv2.cvtColor(segmentation_vis[best_img_name]['segmented_chars'], cv2.COLOR_BGR2RGB))
                plt.title('Character Segmentation')

            if 'char_grid' in segmentation_vis[best_img_name]:
                vis_count += 1
                plt.subplot(2, 2, vis_count)
                plt.imshow(segmentation_vis[best_img_name]['char_grid'], cmap='gray')
                plt.title('Individual Characters')

            if 'all_contours' in segmentation_vis[best_img_name]:
                vis_count += 1
                plt.subplot(2, 2, vis_count)
                plt.imshow(cv2.cvtColor(segmentation_vis[best_img_name]['all_contours'], cv2.COLOR_BGR2RGB))
                plt.title('All Contours')

            if 'filtered_contours' in segmentation_vis[best_img_name]:
                vis_count += 1
                plt.subplot(2, 2, vis_count)
                plt.imshow(cv2.cvtColor(segmentation_vis[best_img_name]['filtered_contours'], cv2.COLOR_BGR2RGB))
                plt.title('Filtered Contours')

            plt.tight_layout()
            plt.show()

            # Show segmentation result
            if 'segmented_text' in segmentation_vis[best_img_name]:
                segmented_text = segmentation_vis[best_img_name]['segmented_text']
                segmented_conf = segmentation_vis[best_img_name]['segmented_confidence']
                print(f"Character-by-character segmentation result: {segmented_text} (Confidence: {segmented_conf:.2f})")
    else:
        text, confidence = read_text_with_multiple_methods(enhanced_plates, reader)
        print(f"Detected Text: {text} (Confidence: {confidence:.2f}%)")

    # Draw results on image
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.drawContours(result_img, [location], 0, (0, 255, 0), 3)
    cv2.putText(result_img, text, (location[0][0][0], location[0][0][1] - 10),
                font, 1, (0, 255, 0), 2, cv2.LINE_AA)
    cv2.putText(result_img, f"Conf: {confidence:.1f}%", (location[0][0][0], location[0][0][1] - 40),
                font, 0.8, (0, 255, 0), 2, cv2.LINE_AA)

    if show_steps:
        plt.figure(figsize=(10, 8))
        plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        plt.title('Final Result')
        plt.show()

    return result_img, text, confidence

def process_image(img_path, show_steps=True):
    """
    Process an image from a file path, URL, or base64 string.

    Args:
        img_path: Path to the image file, URL, or base64 string
        show_steps: Whether to show intermediate steps

    Returns:
        tuple: (result_image, text, confidence)
    """
    # Load image
    img = load_image(img_path)
    if img is None:
        print(f"Error: Image '{img_path}' not found or could not be loaded.")
        return None, "No image found", 0

    # Process the loaded image data
    return process_image_data(img, show_steps)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="License Plate Detection and Recognition")
    parser.add_argument("--image_path", type=str, default="Test1.jpg", help="Path to the input image, URL, or base64 string")
    parser.add_argument("--output_dir", type=str, default="output", help="Directory to save results")
    parser.add_argument("--no_display", action="store_true", help="Don't display intermediate steps")
    parser.add_argument("--format", type=str, choices=["auto", "file", "url", "base64"], default="auto",
                        help="Force specific input format (default: auto-detect)")
    args = parser.parse_args()

    # Process the image
    result_img, text, confidence = process_image(args.image_path, show_steps=not args.no_display)

    if result_img is not None:
        # Create output directory if it doesn't exist
        if not os.path.exists(args.output_dir):
            os.makedirs(args.output_dir)

        # Generate output filename
        if args.image_path.startswith(('http://', 'https://')):
            # For URLs, use the last part of the URL
            base_name = os.path.basename(args.image_path.split('?')[0])  # Remove query parameters
            if not base_name:
                base_name = "url_image.jpg"
        elif args.image_path.startswith(('data:image/', 'base64:')):
            # For base64, use a generic name
            base_name = "base64_image.jpg"
        else:
            # For files, use the filename
            base_name = os.path.basename(args.image_path)

        # Save result image
        output_path = os.path.join(args.output_dir, f"result_{base_name}")
        cv2.imwrite(output_path, result_img)
        print(f"Result saved to {output_path}")

        # Print the detected text and confidence
        print(f"Detected Text: {text}")
        print(f"Confidence: {confidence:.2f}%")
