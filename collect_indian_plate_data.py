#!/usr/bin/env python
# Script to collect and prepare Indian license plate data

import os
import cv2
import numpy as np
import argparse
import shutil
from datetime import datetime
from automatic_number_plate import process_image, segment_characters

def create_dataset_structure(base_dir):
    """
    Create the directory structure for the dataset.
    
    Args:
        base_dir: Base directory for the dataset
    """
    # Create base directory if it doesn't exist
    os.makedirs(base_dir, exist_ok=True)
    
    # Create directories for each character
    chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    for char in chars:
        char_dir = os.path.join(base_dir, char)
        os.makedirs(char_dir, exist_ok=True)
    
    # Create directory for full plates
    plates_dir = os.path.join(base_dir, "plates")
    os.makedirs(plates_dir, exist_ok=True)
    
    # Create directory for metadata
    metadata_dir = os.path.join(base_dir, "metadata")
    os.makedirs(metadata_dir, exist_ok=True)
    
    print(f"Created dataset structure in {base_dir}")

def process_image_for_dataset(img_path, dataset_dir, plate_text=None):
    """
    Process an image and add it to the dataset.
    
    Args:
        img_path: Path to the image
        dataset_dir: Dataset directory
        plate_text: Ground truth license plate text (optional)
        
    Returns:
        bool: True if processing was successful
    """
    try:
        # Process the image to detect license plate
        result_img, detected_text, confidence = process_image(img_path, show_steps=False)
        
        if result_img is None:
            print(f"Failed to process image: {img_path}")
            return False
        
        # Load the original image
        img = cv2.imread(img_path)
        if img is None:
            print(f"Failed to load image: {img_path}")
            return False
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Segment characters
        char_images, vis_images = segment_characters(gray, visualize=True)
        
        if not char_images:
            print(f"No characters segmented in image: {img_path}")
            return False
        
        # Use provided plate text or detected text
        text = plate_text if plate_text else detected_text
        
        # Generate timestamp for unique filenames
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        
        # Save the full plate image
        plates_dir = os.path.join(dataset_dir, "plates")
        plate_filename = f"plate_{timestamp}.jpg"
        plate_path = os.path.join(plates_dir, plate_filename)
        cv2.imwrite(plate_path, result_img)
        
        # Save metadata
        metadata_dir = os.path.join(dataset_dir, "metadata")
        metadata_path = os.path.join(metadata_dir, f"metadata_{timestamp}.txt")
        with open(metadata_path, "w") as f:
            f.write(f"Image: {img_path}\n")
            f.write(f"Plate: {text}\n")
            f.write(f"Confidence: {confidence}\n")
            f.write(f"Characters: {len(char_images)}\n")
            f.write(f"Timestamp: {timestamp}\n")
        
        # If ground truth is provided, save segmented characters
        if plate_text and len(plate_text) == len(char_images):
            for i, (char_img, _) in enumerate(char_images):
                if i < len(plate_text):
                    char = plate_text[i]
                    if char.isalnum():
                        char_dir = os.path.join(dataset_dir, char)
                        char_filename = f"{char}_{timestamp}_{i}.jpg"
                        char_path = os.path.join(char_dir, char_filename)
                        
                        # Resize to standard size (50x50)
                        char_img_resized = cv2.resize(char_img, (50, 50))
                        
                        cv2.imwrite(char_path, char_img_resized)
            
            print(f"Added {len(char_images)} characters from {img_path} to dataset")
            return True
        else:
            print(f"Character count mismatch: {len(char_images)} segmented, {len(plate_text) if plate_text else 0} in ground truth")
            
            # Save the segmentation visualization for manual review
            review_dir = os.path.join(dataset_dir, "review")
            os.makedirs(review_dir, exist_ok=True)
            
            review_path = os.path.join(review_dir, f"review_{timestamp}.jpg")
            cv2.imwrite(review_path, vis_images['segmented_chars'])
            
            # Save individual characters for manual labeling
            chars_dir = os.path.join(review_dir, f"chars_{timestamp}")
            os.makedirs(chars_dir, exist_ok=True)
            
            for i, (char_img, _) in enumerate(char_images):
                char_path = os.path.join(chars_dir, f"char_{i}.jpg")
                cv2.imwrite(char_path, char_img)
            
            print(f"Saved for manual review: {review_path}")
            return False
    
    except Exception as e:
        print(f"Error processing {img_path}: {e}")
        return False

def process_directory(input_dir, dataset_dir, ground_truth_file=None):
    """
    Process all images in a directory and add them to the dataset.
    
    Args:
        input_dir: Directory containing images
        dataset_dir: Dataset directory
        ground_truth_file: Path to ground truth file (optional)
    """
    # Load ground truth if provided
    ground_truth = {}
    if ground_truth_file and os.path.exists(ground_truth_file):
        with open(ground_truth_file, "r") as f:
            for line in f:
                parts = line.strip().split(",")
                if len(parts) >= 2:
                    img_name = parts[0].strip()
                    plate_text = parts[1].strip()
                    ground_truth[img_name] = plate_text
        print(f"Loaded {len(ground_truth)} ground truth entries")
    
    # Process each image in the directory
    success_count = 0
    total_count = 0
    
    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                img_path = os.path.join(root, file)
                total_count += 1
                
                # Get ground truth if available
                plate_text = None
                if file in ground_truth:
                    plate_text = ground_truth[file]
                
                # Process the image
                if process_image_for_dataset(img_path, dataset_dir, plate_text):
                    success_count += 1
    
    print(f"Processed {total_count} images, {success_count} successful")

def manual_labeling_tool(dataset_dir):
    """
    Simple tool for manually labeling characters.
    
    Args:
        dataset_dir: Dataset directory
    """
    review_dir = os.path.join(dataset_dir, "review")
    if not os.path.exists(review_dir):
        print(f"Review directory not found: {review_dir}")
        return
    
    # Find character directories that need labeling
    char_dirs = []
    for item in os.listdir(review_dir):
        if item.startswith("chars_") and os.path.isdir(os.path.join(review_dir, item)):
            char_dirs.append(item)
    
    if not char_dirs:
        print("No character directories found for labeling")
        return
    
    print(f"Found {len(char_dirs)} character sets for labeling")
    
    # Process each character directory
    for char_dir in char_dirs:
        char_dir_path = os.path.join(review_dir, char_dir)
        
        # Get all character images
        char_images = []
        for file in os.listdir(char_dir_path):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                char_images.append(file)
        
        char_images.sort()
        
        if not char_images:
            print(f"No character images found in {char_dir}")
            continue
        
        print(f"\nLabeling characters in {char_dir} ({len(char_images)} images)")
        
        # Show the full plate for reference
        timestamp = char_dir.replace("chars_", "")
        review_img_path = os.path.join(review_dir, f"review_{timestamp}.jpg")
        
        if os.path.exists(review_img_path):
            review_img = cv2.imread(review_img_path)
            cv2.imshow("Plate Image", review_img)
        
        # Label each character
        labels = []
        for i, char_file in enumerate(char_images):
            char_path = os.path.join(char_dir_path, char_file)
            char_img = cv2.imread(char_path)
            
            if char_img is None:
                print(f"Failed to load image: {char_path}")
                labels.append(None)
                continue
            
            # Display the character
            cv2.imshow(f"Character {i+1}", char_img)
            cv2.moveWindow(f"Character {i+1}", 100 + i * 100, 200)
            
            # Get user input
            print(f"Enter label for character {i+1} (or 'q' to quit): ", end="")
            label = input().strip().upper()
            
            if label == 'Q':
                print("Labeling aborted")
                cv2.destroyAllWindows()
                return
            
            if label and len(label) == 1 and label.isalnum():
                labels.append(label)
            else:
                print("Invalid label. Must be a single alphanumeric character.")
                labels.append(None)
            
            cv2.destroyWindow(f"Character {i+1}")
        
        cv2.destroyAllWindows()
        
        # Save labeled characters to dataset
        for i, (char_file, label) in enumerate(zip(char_images, labels)):
            if label is None:
                continue
            
            char_path = os.path.join(char_dir_path, char_file)
            char_img = cv2.imread(char_path, cv2.IMREAD_GRAYSCALE)
            
            if char_img is None:
                continue
            
            # Resize to standard size (50x50)
            char_img_resized = cv2.resize(char_img, (50, 50))
            
            # Save to appropriate character directory
            char_dir = os.path.join(dataset_dir, label)
            if not os.path.exists(char_dir):
                os.makedirs(char_dir)
            
            char_filename = f"{label}_{timestamp}_{i}.jpg"
            output_path = os.path.join(char_dir, char_filename)
            cv2.imwrite(output_path, char_img_resized)
        
        print(f"Saved {sum(1 for l in labels if l is not None)} labeled characters")
        
        # Mark directory as processed
        processed_dir = os.path.join(review_dir, f"processed_{char_dir}")
        shutil.move(char_dir_path, processed_dir)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Collect and prepare Indian license plate data")
    parser.add_argument("--input", type=str, help="Input directory containing images")
    parser.add_argument("--dataset", type=str, default="indian_plate_dataset", help="Dataset directory")
    parser.add_argument("--ground_truth", type=str, help="Path to ground truth file (CSV format: image_name,plate_text)")
    parser.add_argument("--create", action="store_true", help="Create dataset structure")
    parser.add_argument("--label", action="store_true", help="Run manual labeling tool")
    
    args = parser.parse_args()
    
    if args.create:
        create_dataset_structure(args.dataset)
    
    if args.input:
        process_directory(args.input, args.dataset, args.ground_truth)
    
    if args.label:
        manual_labeling_tool(args.dataset)
