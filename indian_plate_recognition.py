#!/usr/bin/env python
# Indian license plate recognition using custom trained model

import os
import cv2
import numpy as np
import tensorflow as tf
from automatic_number_plate import process_image, segment_characters

# Constants
IMG_HEIGHT = 50
IMG_WIDTH = 50
INDIAN_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
INDEX_TO_CHAR = {i: char for i, char in enumerate(INDIAN_CHARS)}

# Indian state codes for validation
INDIAN_STATE_CODES = [
    'AP', 'AR', 'AS', 'BR', 'CG', 'CH', 'DD', 'DL', 'DN', 'GA', 'GJ', 
    'HP', 'HR', 'JH', 'JK', 'KA', 'KL', 'LA', 'LD', 'MH', 'ML', 'MN', 
    'MP', 'MZ', 'NL', 'OD', 'PB', 'PY', 'RJ', 'SK', 'TN', 'TR', 'TS', 
    'UK', 'UP', 'WB'
]

def load_model(model_path):
    """
    Load the trained model for Indian license plate character recognition.
    
    Args:
        model_path: Path to the trained model
        
    Returns:
        Loaded model
    """
    try:
        model = tf.keras.models.load_model(model_path)
        print(f"Model loaded from {model_path}")
        return model
    except Exception as e:
        print(f"Error loading model: {e}")
        return None

def preprocess_char_for_prediction(char_img):
    """
    Preprocess a character image for prediction.
    
    Args:
        char_img: Character image
        
    Returns:
        Preprocessed image ready for model input
    """
    # Resize to standard size
    img = cv2.resize(char_img, (IMG_WIDTH, IMG_HEIGHT))
    
    # Normalize pixel values
    img = img / 255.0
    
    # Reshape for model input
    img = img.reshape(1, IMG_HEIGHT, IMG_WIDTH, 1)
    
    return img

def recognize_indian_plate(img_path, model_path, show_steps=True):
    """
    Recognize Indian license plate using custom trained model.
    
    Args:
        img_path: Path to the image
        model_path: Path to the trained model
        show_steps: Whether to show intermediate steps
        
    Returns:
        tuple: (result_image, text, confidence)
    """
    # Load the model
    model = load_model(model_path)
    if model is None:
        return None, "Model loading failed", 0
    
    # Process the image to detect license plate
    result_img, text, confidence = process_image(img_path, show_steps=show_steps)
    
    # If standard processing found a plate with good confidence, return it
    if confidence > 80:
        print(f"Standard processing found plate with high confidence: {text} ({confidence:.2f}%)")
        return result_img, text, confidence
    
    # Otherwise, try our custom approach with the trained model
    print("Using custom Indian license plate recognition...")
    
    # Load the image
    img = cv2.imread(img_path)
    if img is None:
        print(f"Error: Could not load image {img_path}")
        return None, "Image loading failed", 0
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Segment characters
    if show_steps:
        char_images, vis_images = segment_characters(gray, visualize=True)
        
        # Show segmentation visualization
        if 'segmented_chars' in vis_images:
            cv2.imshow("Character Segmentation", vis_images['segmented_chars'])
            cv2.waitKey(0)
            cv2.destroyAllWindows()
    else:
        char_images = segment_characters(gray)
    
    if not char_images:
        print("No characters segmented")
        return result_img, text, confidence
    
    # Recognize each character using our trained model
    recognized_chars = []
    char_confidences = []
    
    for char_img, _ in char_images:
        # Preprocess character image
        processed_img = preprocess_char_for_prediction(char_img)
        
        # Predict
        prediction = model.predict(processed_img, verbose=0)
        predicted_class = np.argmax(prediction)
        char_confidence = prediction[0][predicted_class]
        
        # Get character
        predicted_char = INDEX_TO_CHAR[predicted_class]
        
        recognized_chars.append(predicted_char)
        char_confidences.append(char_confidence)
        
        if show_steps:
            print(f"Recognized character: {predicted_char} (Confidence: {char_confidence:.2f})")
    
    # Combine characters into a single text
    custom_text = ''.join(recognized_chars)
    custom_confidence = np.mean(char_confidences) * 100
    
    print(f"Custom recognition result: {custom_text} (Confidence: {custom_confidence:.2f}%)")
    
    # Validate and format the license plate text
    formatted_text = format_indian_plate(custom_text)
    if formatted_text:
        custom_text = formatted_text
        custom_confidence += 10  # Boost confidence if we could format it
    
    # Draw results on image
    font = cv2.FONT_HERSHEY_SIMPLEX
    h, w = img.shape[:2]
    
    # Draw a rectangle around the license plate area
    cv2.rectangle(result_img, (10, 10), (w-10, h-10), (0, 255, 0), 3)
    
    # Position text at the top of the image
    cv2.putText(result_img, custom_text, (w//10, 30), 
                font, 1, (0, 255, 0), 2, cv2.LINE_AA)
    cv2.putText(result_img, f"Conf: {custom_confidence:.1f}%", (w//10, 70), 
                font, 0.8, (0, 255, 0), 2, cv2.LINE_AA)
    
    if show_steps:
        cv2.imshow("Custom Recognition Result", result_img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    
    # Return the better result (higher confidence)
    if custom_confidence > confidence:
        return result_img, custom_text, custom_confidence
    else:
        return result_img, text, confidence

def format_indian_plate(text):
    """
    Format and validate Indian license plate text.
    
    Args:
        text: Recognized text
        
    Returns:
        Formatted text or None if invalid
    """
    # Remove any spaces or special characters
    text = ''.join(c for c in text if c.isalnum())
    
    # Check if it's too short
    if len(text) < 6:
        return None
    
    # Try to identify state code
    state_code = None
    for code in INDIAN_STATE_CODES:
        if text.startswith(code):
            state_code = code
            break
    
    if not state_code and len(text) >= 2:
        # Try to extract first two characters as state code
        potential_code = text[:2]
        if potential_code.isalpha():
            state_code = potential_code
    
    if not state_code:
        return None
    
    # Extract remaining parts
    remaining = text[len(state_code):]
    
    # Modern Indian format: AA-00-AA-0000
    # State code (2 letters) + District code (2 digits) + Series (2 letters) + Number (4 digits)
    
    # Try to identify district code (1-2 digits)
    district_code = ""
    district_end = 0
    for i, char in enumerate(remaining):
        if char.isdigit():
            district_code += char
            district_end = i + 1
            if len(district_code) == 2:
                break
        else:
            break
    
    if not district_code:
        return None
    
    # Extract series (1-2 letters)
    remaining = remaining[district_end:]
    series = ""
    series_end = 0
    for i, char in enumerate(remaining):
        if char.isalpha():
            series += char
            series_end = i + 1
            if len(series) == 2:
                break
        else:
            break
    
    if not series:
        return None
    
    # Extract number (1-4 digits)
    remaining = remaining[series_end:]
    number = ""
    for char in remaining:
        if char.isdigit():
            number += char
            if len(number) == 4:
                break
        else:
            break
    
    if not number:
        return None
    
    # Format the license plate text
    formatted = f"{state_code}{district_code}{series}{number}"
    
    return formatted

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Indian License Plate Recognition")
    parser.add_argument("--image", type=str, required=True, help="Path to the image")
    parser.add_argument("--model", type=str, default="indian_plate_model.h5", help="Path to the trained model")
    parser.add_argument("--no_display", action="store_true", help="Don't display intermediate steps")
    parser.add_argument("--output", type=str, default="result.jpg", help="Path to save the result image")
    
    args = parser.parse_args()
    
    # Recognize license plate
    result_img, text, confidence = recognize_indian_plate(
        args.image, 
        args.model, 
        show_steps=not args.no_display
    )
    
    if result_img is not None:
        # Save result
        cv2.imwrite(args.output, result_img)
        print(f"Result saved to {args.output}")
        
        # Print the detected text and confidence
        print(f"Detected Text: {text}")
        print(f"Confidence: {confidence:.2f}%")
