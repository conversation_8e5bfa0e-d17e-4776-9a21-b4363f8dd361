#!/usr/bin/env python
# Training script for Indian license plate recognition with persistence features

import os
import cv2
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import datetime
import glob

# Define constants
INDIAN_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
CHAR_TO_INDEX = {char: i for i, char in enumerate(INDIAN_CHARS)}
INDEX_TO_CHAR = {i: char for i, char in enumerate(INDIAN_CHARS)}
IMG_HEIGHT = 50
IMG_WIDTH = 50
BATCH_SIZE = 32
EPOCHS = 20

def load_dataset(dataset_path):
    """
    Load the dataset of segmented characters from Indian license plates.
    
    Args:
        dataset_path: Path to the dataset directory
        
    Returns:
        X: Image data
        y: Labels
    """
    X = []
    y = []
    
    # Each subdirectory is named after the character it contains
    for char_dir in os.listdir(dataset_path):
        char_path = os.path.join(dataset_path, char_dir)
        if os.path.isdir(char_path) and char_dir in INDIAN_CHARS:
            for img_file in os.listdir(char_path):
                img_path = os.path.join(char_path, img_file)
                try:
                    # Load and preprocess image
                    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                    if img is None:
                        continue
                    
                    # Resize to standard size
                    img = cv2.resize(img, (IMG_WIDTH, IMG_HEIGHT))
                    
                    # Normalize pixel values
                    img = img / 255.0
                    
                    # Add to dataset
                    X.append(img)
                    y.append(CHAR_TO_INDEX[char_dir])
                except Exception as e:
                    print(f"Error processing {img_path}: {e}")
    
    return np.array(X), np.array(y)

def build_model():
    """
    Build a CNN model for character recognition.
    
    Returns:
        Compiled Keras model
    """
    model = models.Sequential([
        # Input layer
        layers.Input(shape=(IMG_HEIGHT, IMG_WIDTH, 1)),
        
        # First convolutional block
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        # Second convolutional block
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        # Third convolutional block
        layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        # Flatten and dense layers
        layers.Flatten(),
        layers.Dropout(0.5),
        layers.Dense(128, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        layers.Dense(len(INDIAN_CHARS), activation='softmax')
    ])
    
    # Compile the model
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

def train_model(dataset_path, output_model_path, resume_checkpoint=None):
    """
    Train a model on the Indian license plate character dataset with persistence.
    
    Args:
        dataset_path: Path to the dataset directory
        output_model_path: Path to save the trained model
        resume_checkpoint: Path to checkpoint to resume training from
        
    Returns:
        Trained model
    """
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_model_path), exist_ok=True)
    
    # Update progress
    progress_file = os.path.join(os.path.dirname(output_model_path), "training_progress.txt")
    with open(progress_file, "a") as f:
        f.write(f"{datetime.datetime.now()}: Starting model training\n")
    
    # Load dataset
    print("Loading dataset...")
    X, y = load_dataset(dataset_path)
    
    if len(X) == 0:
        print("Error: No data found in the dataset.")
        with open(progress_file, "a") as f:
            f.write(f"{datetime.datetime.now()}: Error - No data found in the dataset\n")
        return None
    
    # Reshape for CNN input (add channel dimension)
    X = X.reshape(X.shape[0], IMG_HEIGHT, IMG_WIDTH, 1)
    
    # Split into training and validation sets
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)
    
    print(f"Training set: {X_train.shape[0]} samples")
    print(f"Validation set: {X_val.shape[0]} samples")
    
    with open(progress_file, "a") as f:
        f.write(f"{datetime.datetime.now()}: Dataset loaded - {X_train.shape[0]} training samples, {X_val.shape[0]} validation samples\n")
    
    # Build model
    print("Building model...")
    model = build_model()
    model.summary()
    
    # If resuming from checkpoint, load weights
    if resume_checkpoint and os.path.exists(resume_checkpoint):
        print(f"Loading weights from checkpoint: {resume_checkpoint}")
        model.load_weights(resume_checkpoint)
        with open(progress_file, "a") as f:
            f.write(f"{datetime.datetime.now()}: Resumed training from checkpoint: {resume_checkpoint}\n")
    
    # Data augmentation
    data_augmentation = tf.keras.Sequential([
        layers.RandomRotation(0.1),
        layers.RandomZoom(0.1),
        layers.RandomContrast(0.1),
    ])
    
    # Create checkpoint directory
    checkpoint_dir = os.path.dirname(output_model_path)
    checkpoint_path = os.path.join(checkpoint_dir, 'checkpoint_epoch_{epoch:02d}_val_acc_{val_accuracy:.2f}.h5')
    
    # Create callbacks
    callbacks = [
        # Early stopping to prevent overfitting
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=5,
            restore_best_weights=True,
            verbose=1
        ),
        
        # Reduce learning rate when training plateaus
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.2,
            patience=3,
            min_lr=1e-6,
            verbose=1
        ),
        
        # Save checkpoints during training
        tf.keras.callbacks.ModelCheckpoint(
            filepath=checkpoint_path,
            monitor='val_accuracy',
            save_best_only=True,
            save_weights_only=False,
            mode='max',
            verbose=1
        ),
        
        # TensorBoard logging
        tf.keras.callbacks.TensorBoard(
            log_dir=os.path.join(checkpoint_dir, 'logs', datetime.datetime.now().strftime("%Y%m%d-%H%M%S")),
            histogram_freq=1,
            write_graph=True,
            update_freq='epoch'
        ),
        
        # CSV Logger for detailed metrics
        tf.keras.callbacks.CSVLogger(
            os.path.join(checkpoint_dir, 'training_log.csv'),
            append=True
        )
    ]
    
    # Train model
    print("Training model...")
    with open(progress_file, "a") as f:
        f.write(f"{datetime.datetime.now()}: Starting model training for up to {EPOCHS} epochs\n")
    
    history = model.fit(
        tf.data.Dataset.from_tensor_slices((X_train, y_train))
            .map(lambda x, y: (data_augmentation(x, training=True), y))
            .batch(BATCH_SIZE),
        epochs=EPOCHS,
        validation_data=(X_val, y_val),
        callbacks=callbacks
    )
    
    # Plot training history
    plt.figure(figsize=(12, 4))
    plt.subplot(1, 2, 1)
    plt.plot(history.history['accuracy'])
    plt.plot(history.history['val_accuracy'])
    plt.title('Model Accuracy')
    plt.ylabel('Accuracy')
    plt.xlabel('Epoch')
    plt.legend(['Train', 'Validation'], loc='lower right')
    
    plt.subplot(1, 2, 2)
    plt.plot(history.history['loss'])
    plt.plot(history.history['val_loss'])
    plt.title('Model Loss')
    plt.ylabel('Loss')
    plt.xlabel('Epoch')
    plt.legend(['Train', 'Validation'], loc='upper right')
    plt.tight_layout()
    
    history_plot_path = os.path.join(os.path.dirname(output_model_path), 'training_history.png')
    plt.savefig(history_plot_path)
    
    # Evaluate model
    print("Evaluating model...")
    test_loss, test_acc = model.evaluate(X_val, y_val)
    print(f"Test accuracy: {test_acc:.4f}")
    
    with open(progress_file, "a") as f:
        f.write(f"{datetime.datetime.now()}: Model evaluation - Test accuracy: {test_acc:.4f}\n")
    
    # Save model
    print(f"Saving model to {output_model_path}...")
    model.save(output_model_path)
    
    # Save model in TensorFlow Lite format for mobile deployment
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    tflite_model = converter.convert()
    tflite_path = os.path.splitext(output_model_path)[0] + '.tflite'
    with open(tflite_path, 'wb') as f:
        f.write(tflite_model)
    print(f"TensorFlow Lite model saved to {tflite_path}")
    
    with open(progress_file, "a") as f:
        f.write(f"{datetime.datetime.now()}: Training completed - Model saved to {output_model_path}\n")
    
    return model

def test_model(model, test_image_path):
    """
    Test the model on a single image.
    
    Args:
        model: Trained model
        test_image_path: Path to test image
    """
    # Load and preprocess image
    img = cv2.imread(test_image_path, cv2.IMREAD_GRAYSCALE)
    if img is None:
        print(f"Error: Could not load image {test_image_path}")
        return
    
    # Resize to standard size
    img = cv2.resize(img, (IMG_WIDTH, IMG_HEIGHT))
    
    # Normalize pixel values
    img = img / 255.0
    
    # Reshape for model input
    img = img.reshape(1, IMG_HEIGHT, IMG_WIDTH, 1)
    
    # Predict
    prediction = model.predict(img)
    predicted_class = np.argmax(prediction)
    confidence = prediction[0][predicted_class]
    
    # Get character
    predicted_char = INDEX_TO_CHAR[predicted_class]
    
    print(f"Predicted character: {predicted_char} (Confidence: {confidence:.2f})")
    
    # Display image and prediction
    plt.figure(figsize=(4, 4))
    plt.imshow(img.reshape(IMG_HEIGHT, IMG_WIDTH), cmap='gray')
    plt.title(f"Predicted: {predicted_char} ({confidence:.2f})")
    plt.axis('off')
    plt.show()

def generate_synthetic_data(output_dir, num_samples=1000):
    """
    Generate synthetic Indian license plate characters for training.
    
    Args:
        output_dir: Directory to save generated images
        num_samples: Number of samples to generate per character
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Update progress
    progress_file = os.path.join(output_dir, "..", "training_progress.txt")
    if os.path.exists(os.path.dirname(progress_file)):
        with open(progress_file, "a") as f:
            f.write(f"{datetime.datetime.now()}: Starting synthetic data generation - {num_samples} samples per character\n")
    
    # For each character in the Indian license plate character set
    for char in INDIAN_CHARS:
        # Create character directory
        char_dir = os.path.join(output_dir, char)
        os.makedirs(char_dir, exist_ok=True)
        
        # Generate samples
        for i in range(num_samples):
            # Create a blank image
            img = np.ones((100, 100), dtype=np.uint8) * 255
            
            # Choose a random font
            font = cv2.FONT_HERSHEY_SIMPLEX
            if np.random.rand() > 0.5:
                font = cv2.FONT_HERSHEY_COMPLEX
            
            # Random scale and thickness
            scale = 0.8 + np.random.rand() * 1.5
            thickness = 1 + int(np.random.rand() * 3)
            
            # Get text size
            (text_width, text_height), _ = cv2.getTextSize(char, font, scale, thickness)
            
            # Calculate position to center text
            x = int((100 - text_width) / 2)
            y = int((100 + text_height) / 2)
            
            # Add random offset
            x += int(np.random.randn() * 5)
            y += int(np.random.randn() * 5)
            
            # Draw text
            cv2.putText(img, char, (x, y), font, scale, 0, thickness)
            
            # Apply random transformations
            # 1. Rotation
            angle = np.random.randn() * 10  # Random angle between -10 and 10 degrees
            M = cv2.getRotationMatrix2D((50, 50), angle, 1)
            img = cv2.warpAffine(img, M, (100, 100))
            
            # 2. Noise
            if np.random.rand() > 0.5:
                noise = np.random.randn(100, 100) * 10
                img = np.clip(img + noise, 0, 255).astype(np.uint8)
            
            # 3. Blur
            if np.random.rand() > 0.7:
                kernel_size = 1 + 2 * int(np.random.rand() * 2)
                img = cv2.GaussianBlur(img, (kernel_size, kernel_size), 0)
            
            # Save image
            output_path = os.path.join(char_dir, f"{char}_{i:04d}.png")
            cv2.imwrite(output_path, img)
        
        print(f"Generated {num_samples} samples for character '{char}'")
    
    if os.path.exists(os.path.dirname(progress_file)):
        with open(progress_file, "a") as f:
            f.write(f"{datetime.datetime.now()}: Completed synthetic data generation\n")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Train a model for Indian license plate character recognition")
    parser.add_argument("--dataset", type=str, default="indian_plate_chars", help="Path to dataset directory")
    parser.add_argument("--model", type=str, default="indian_plate_model.h5", help="Path to save the trained model")
    parser.add_argument("--generate", action="store_true", help="Generate synthetic data")
    parser.add_argument("--samples", type=int, default=1000, help="Number of synthetic samples per character")
    parser.add_argument("--test", type=str, help="Path to test image")
    parser.add_argument("--resume", type=str, help="Path to checkpoint to resume training from")
    
    args = parser.parse_args()
    
    if args.generate:
        print(f"Generating synthetic data ({args.samples} samples per character)...")
        generate_synthetic_data(args.dataset, args.samples)
    
    if os.path.exists(args.dataset):
        model = train_model(args.dataset, args.model, args.resume)
        
        if args.test and model is not None:
            test_model(model, args.test)
    else:
        print(f"Error: Dataset directory '{args.dataset}' not found.")
        print("Use --generate to create synthetic data first.")