import cv2
import numpy as np
import os
import argparse
from license_plate_recognition import LicensePlateRecognition

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test the optimized license plate recognition model')
    parser.add_argument('--image', type=str, help='Path to the image to process')
    args = parser.parse_args()

    # Initialize the license plate recognition system
    lpr = LicensePlateRecognition()
    
    # Process the image
    if args.image:
        # Process a single image
        if not os.path.exists(args.image):
            print(f"Error: Image file {args.image} not found")
            return
        
        print(f"Processing image: {args.image}")
        img = cv2.imread(args.image)
        
        if img is None:
            print(f"Error: Could not read image {args.image}")
            return
        
        # Process the image
        result_img, results = lpr.process_image(img)
        
        # Display results
        print(f"Results for {args.image}:")
        if results:
            for result in results:
                print(f"Plate: {result['plate_number']}, Confidence: {result['confidence']:.2f}%")
                if 'note' in result:
                    print(f"Note: {result['note']}")
        else:
            print("No license plates detected")
        
        # Save the result image
        result_path = f"results_{os.path.basename(args.image)}"
        cv2.imwrite(result_path, result_img)
        print(f"Result image saved to {result_path}")
        
        # Display the image
        cv2.imshow("Result", result_img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    else:
        # Test with the challenging examples
        test_images = [
            {"path": "test_images/white_alto.jpg", "expected": "MH01BT1699", "description": "White Maruti Suzuki Alto"},
            {"path": "test_images/white_omni.jpg", "expected": "AP09AF153", "description": "White Maruti Suzuki Omni van"},
            {"path": "test_images/rj14cv0002.jpg", "expected": "RJ14CV0002", "description": "Standard Indian plate"}
        ]
        
        for test_case in test_images:
            path = test_case["path"]
            expected = test_case["expected"]
            description = test_case["description"]
            
            if not os.path.exists(path):
                print(f"Warning: Test image {path} not found, skipping")
                continue
                
            print(f"\nProcessing test case: {description}")
            print(f"Expected plate: {expected}")
            
            img = cv2.imread(path)
            if img is None:
                print(f"Error: Could not read image {path}")
                continue
                
            # Process the image
            result_img, results = lpr.process_image(img)
            
            # Display results
            if results:
                for result in results:
                    plate = result['plate_number']
                    confidence = result['confidence']
                    print(f"Detected plate: {plate}, Confidence: {confidence:.2f}%")
                    if plate == expected:
                        print("✓ MATCH!")
                    else:
                        print("✗ NO MATCH")
            else:
                print("No license plates detected")
                
            # Save the result image
            result_path = f"results_{os.path.basename(path)}"
            cv2.imwrite(result_path, result_img)
            print(f"Result image saved to {result_path}")

if __name__ == "__main__":
    main()
