import cv2
import numpy as np
from skimage import exposure
import logging

class ImageEnhancer:
    """
    A class for enhancing image quality to improve license plate detection and recognition,
    especially in low quality or less detailed images.
    """

    def __init__(self):
        self.logger = logging.getLogger('License Plate Recognition')

    def enhance_image(self, image):
        """
        Apply multiple enhancement techniques to improve image quality.
        Optimized for performance by focusing on the most effective techniques.

        Args:
            image: Input image (numpy array)

        Returns:
            list: List of enhanced images with different techniques
        """
        if image is None:
            self.logger.error("Cannot enhance None image")
            return []

        enhanced_images = []

        # Store the original image
        enhanced_images.append(("original", image.copy()))

        try:
            # Convert to grayscale if color image
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                enhanced_images.append(("grayscale", gray))
            else:
                gray = image.copy()

            # 1. Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            # This is one of the most effective techniques for license plate recognition
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            clahe_img = clahe.apply(gray)
            enhanced_images.append(("clahe", clahe_img))

            # 2. Apply stronger CLAHE for very low contrast images
            clahe_strong = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(4, 4))
            clahe_strong_img = clahe_strong.apply(gray)
            enhanced_images.append(("clahe_strong", clahe_strong_img))

            # 3. Apply denoising - critical for low quality images
            denoised = cv2.fastNlMeansDenoising(gray, None, 10, 7, 21)
            enhanced_images.append(("denoised", denoised))

            # 4. Apply bilateral filter (edge-preserving smoothing)
            # Excellent for preserving edges while removing noise
            bilateral = cv2.bilateralFilter(gray, 9, 75, 75)
            enhanced_images.append(("bilateral", bilateral))

            # 5. Apply adaptive thresholding - good for varying lighting conditions
            adaptive_thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            enhanced_images.append(("adaptive_thresh", adaptive_thresh))

            # 6. Apply Otsu's thresholding - good for bimodal images
            _, otsu_thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            enhanced_images.append(("otsu_thresh", otsu_thresh))

            # 7. Apply sharpening - helps with blurry images
            kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
            sharpened = cv2.filter2D(gray, -1, kernel)
            enhanced_images.append(("sharpened", sharpened))

            # 8. Apply edge enhancement - helps with low contrast plates
            edge_enhanced = cv2.Canny(gray, 50, 150)
            dilated_edges = cv2.dilate(edge_enhanced, np.ones((2, 2), np.uint8), iterations=1)
            enhanced_images.append(("edge_enhanced", dilated_edges))

            # 9. Apply gamma correction for different gamma values
            # This helps with under or overexposed images
            gamma_values = [0.7, 1.5]  # Reduced to most effective values
            for gamma in gamma_values:
                gamma_corrected = self._adjust_gamma(gray, gamma)
                enhanced_images.append((f"gamma_{gamma}", gamma_corrected))

            # 10. Apply combinations of techniques (most effective combinations)
            # CLAHE + Denoising - excellent for noisy, low contrast images
            clahe_denoised = cv2.fastNlMeansDenoising(clahe_img, None, 10, 7, 21)
            enhanced_images.append(("clahe_denoised", clahe_denoised))

            # CLAHE + Bilateral - preserves edges while enhancing contrast
            clahe_bilateral = cv2.bilateralFilter(clahe_img, 9, 75, 75)
            enhanced_images.append(("clahe_bilateral", clahe_bilateral))

            # Denoised + Adaptive threshold - good for noisy images with text
            denoised_thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            enhanced_images.append(("denoised_thresh", denoised_thresh))

            # 11. Apply morphological operations for specific cases
            kernel_morph = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

            # Closing to fill gaps in characters
            closed = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel_morph)
            enhanced_images.append(("closed_thresh", closed))

            # 12. Special enhancement for Indian license plates
            # Higher contrast and sharper edges
            special_indian = cv2.addWeighted(clahe_img, 1.5, cv2.GaussianBlur(clahe_img, (0, 0), 3.0), -0.5, 0)
            enhanced_images.append(("special_indian", special_indian))

            # 13. Apply local contrast enhancement for very low quality images
            try:
                local_contrast = exposure.equalize_adapthist(gray, clip_limit=0.03)
                local_contrast = (local_contrast * 255).astype(np.uint8)
                enhanced_images.append(("local_contrast", local_contrast))
            except Exception as e:
                self.logger.error(f"Error applying local contrast enhancement: {str(e)}")

            self.logger.info(f"Generated {len(enhanced_images)} enhanced versions of the image")
            return enhanced_images

        except Exception as e:
            self.logger.error(f"Error in image enhancement: {str(e)}")
            # Return the original image if enhancement fails
            return [("original", image.copy())]

    def enhance_for_plate_detection(self, image):
        """
        Apply enhancements specifically optimized for license plate detection.

        Args:
            image: Input image (numpy array)

        Returns:
            list: List of enhanced images optimized for plate detection
        """
        enhanced_images = []

        try:
            # Convert to grayscale if color image
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 1. Apply bilateral filter to preserve edges
            bilateral = cv2.bilateralFilter(gray, 11, 17, 17)
            enhanced_images.append(("bilateral", bilateral))

            # 2. Apply CLAHE for better contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            clahe_img = clahe.apply(gray)
            enhanced_images.append(("clahe", clahe_img))

            # 3. Apply edge enhancement
            edge_kernel = np.array([[-1, -1, -1, -1, -1],
                                   [-1, 2, 2, 2, -1],
                                   [-1, 2, 8, 2, -1],
                                   [-1, 2, 2, 2, -1],
                                   [-1, -1, -1, -1, -1]]) / 8.0
            edge_enhanced = cv2.filter2D(gray, -1, edge_kernel)
            enhanced_images.append(("edge_enhanced", edge_enhanced))

            # 4. Apply Canny edge detection with adaptive thresholds
            median = np.median(gray)
            lower = int(max(0, 0.66 * median))
            upper = int(min(255, 1.33 * median))
            edges = cv2.Canny(gray, lower, upper)
            enhanced_images.append(("canny_adaptive", edges))

            # 5. Apply morphological operations to connect edges
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
            enhanced_images.append(("edges_closed", edges_closed))

            # 6. Apply thresholding for binary image
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            enhanced_images.append(("otsu_thresh", thresh))

            # 7. Apply adaptive thresholding
            adaptive_thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            enhanced_images.append(("adaptive_thresh", adaptive_thresh))

            return enhanced_images

        except Exception as e:
            self.logger.error(f"Error in plate detection enhancement: {str(e)}")
            # Return the original grayscale image if enhancement fails
            if len(image.shape) == 3:
                return [("original_gray", cv2.cvtColor(image, cv2.COLOR_BGR2GRAY))]
            else:
                return [("original_gray", image.copy())]

    def enhance_for_ocr(self, plate_img):
        """
        Apply enhancements specifically optimized for OCR on license plates.

        Args:
            plate_img: License plate image (numpy array)

        Returns:
            list: List of enhanced images optimized for OCR
        """
        enhanced_images = []

        try:
            # Convert to grayscale if color image
            if len(plate_img.shape) == 3:
                gray = cv2.cvtColor(plate_img, cv2.COLOR_BGR2GRAY)
            else:
                gray = plate_img.copy()

            # Resize for better OCR (larger images work better with OCR)
            h, w = gray.shape
            scale_factor = 4.0  # Increased scale factor for better detail
            if w < 100 or h < 30:  # Only resize small images
                gray = cv2.resize(gray, None, fx=scale_factor, fy=scale_factor,
                                 interpolation=cv2.INTER_CUBIC)

            # 1. Original grayscale
            enhanced_images.append(("original", gray))

            # 2. Apply CLAHE for better contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            clahe_img = clahe.apply(gray)
            enhanced_images.append(("clahe", clahe_img))

            # 3. Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray, None, 10, 7, 21)
            enhanced_images.append(("denoised", denoised))

            # 4. Apply sharpening
            kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
            sharpened = cv2.filter2D(gray, -1, kernel)
            enhanced_images.append(("sharpened", sharpened))

            # 5. Apply adaptive thresholding - Gaussian
            thresh_gauss = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            enhanced_images.append(("thresh_gauss", thresh_gauss))

            # 6. Apply adaptive thresholding - Mean
            thresh_mean = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 11, 2)
            enhanced_images.append(("thresh_mean", thresh_mean))

            # 7. Apply Otsu's thresholding
            _, thresh_otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            enhanced_images.append(("thresh_otsu", thresh_otsu))

            # 8. Apply local contrast enhancement
            try:
                from skimage import exposure
                local_contrast = exposure.equalize_adapthist(gray, clip_limit=0.03)
                local_contrast = (local_contrast * 255).astype(np.uint8)
                enhanced_images.append(("local_contrast", local_contrast))
            except:
                pass

            # 9. Apply bilateral filter
            bilateral = cv2.bilateralFilter(gray, 11, 17, 17)
            enhanced_images.append(("bilateral", bilateral))

            # 10. Apply combinations
            # CLAHE + Denoising
            clahe_denoised = cv2.fastNlMeansDenoising(clahe_img, None, 10, 7, 21)
            enhanced_images.append(("clahe_denoised", clahe_denoised))

            # CLAHE + Sharpening
            clahe_sharpened = cv2.filter2D(clahe_img, -1, kernel)
            enhanced_images.append(("clahe_sharpened", clahe_sharpened))

            # Denoised + Adaptive threshold
            denoised_thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            enhanced_images.append(("denoised_thresh", denoised_thresh))

            # 11. Apply morphological operations
            kernel_morph = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

            # Opening to remove noise
            opened = cv2.morphologyEx(thresh_gauss, cv2.MORPH_OPEN, kernel_morph)
            enhanced_images.append(("opened", opened))

            # Closing to fill gaps
            closed = cv2.morphologyEx(thresh_gauss, cv2.MORPH_CLOSE, kernel_morph)
            enhanced_images.append(("closed", closed))

            # 12. Inverted versions for dark text on light background
            for name, img in enhanced_images.copy():
                enhanced_images.append((f"{name}_inv", cv2.bitwise_not(img)))

            return enhanced_images

        except Exception as e:
            self.logger.error(f"Error in OCR enhancement: {str(e)}")
            # Return the original image if enhancement fails
            if len(plate_img.shape) == 3:
                return [("original", cv2.cvtColor(plate_img, cv2.COLOR_BGR2GRAY))]
            else:
                return [("original", plate_img.copy())]

    def _adjust_gamma(self, image, gamma=1.0):
        """
        Adjust gamma of an image.

        Args:
            image: Input image
            gamma: Gamma value (default: 1.0)

        Returns:
            Gamma-corrected image
        """
        # Build a lookup table mapping pixel values [0, 255] to adjusted gamma values
        inv_gamma = 1.0 / gamma
        table = np.array([
            ((i / 255.0) ** inv_gamma) * 255 for i in range(256)
        ]).astype(np.uint8)

        # Apply the lookup table
        return cv2.LUT(image, table)
